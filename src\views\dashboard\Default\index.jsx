import { useEffect, useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid2';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Alert from '@mui/material/Alert';
import BarChartIcon from '@mui/icons-material/BarChart';
import AssessmentIcon from '@mui/icons-material/Assessment';

// project imports
import TotalProductsCard from './TotalProductsCard';
import TotalOrdersCard from './TotalOrdersCard';
import TotalClientsCard from './TotalClientsCard';
import TotalRevenueCard from './TotalRevenueCard';
import RecentOrdersCard from './RecentOrdersCard';
import SalesGrowthChart from './SalesGrowthChart';
import SalesMetricsCard from './SalesMetricsCard';
import SimpleSalesChart from './SimpleSalesChart';
import MainCard from 'ui-component/cards/MainCard';
import StandardButton from 'ui-component/buttons/StandardButton';

import { gridSpacing } from 'store/constant';

// Design system
import { COLORS, TYPOGRAPHY } from 'themes/designSystem';

// services
import { fetchDashboardMetrics } from '../../../services/dashboardService';

// ==============================|| DEFAULT DASHBOARD ||============================== //

export default function Dashboard() {
  const [isLoading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    totalProducts: 0,
    totalOrders: 0,
    totalClients: 0,
    totalRevenue: 0,
    recentOrders: [],
    salesData: []
  });
  const [error, setError] = useState(null);
  const [useMetricsCard, setUseMetricsCard] = useState(true); // Use metrics card by default

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const data = await fetchDashboardMetrics();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError(`Erreur lors du chargement des données: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  return (
    <MainCard>
      <Box sx={{ width: '100%' }}>
        {/* Breadcrumb - Design System Style */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="body2"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.sm
            }}
          >
            Accueil &gt; Dashboard
          </Typography>
        </Box>

        {/* Header - Design System Style */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h3"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              fontWeight: TYPOGRAPHY.fontWeight.bold,
              color: COLORS.text.dark,
              mb: 1
            }}
          >
            Dashboard
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontFamily: TYPOGRAPHY.fontFamily.primary,
              color: COLORS.text.secondary,
              fontSize: TYPOGRAPHY.fontSize.md
            }}
          >
            Vue d'ensemble de votre activité et métriques clés
          </Typography>
        </Box>

        {/* Error Display */}
        {error && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="error" onClose={() => setError('')}>
              <strong>Erreur de chargement:</strong> {error}
            </Alert>
          </Box>
        )}

        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Grid container spacing={gridSpacing}>
              <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
                <TotalProductsCard isLoading={isLoading} total={dashboardData.totalProducts} error={error} />
              </Grid>
              <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
                <TotalOrdersCard isLoading={isLoading} total={dashboardData.totalOrders} error={error} />
              </Grid>
              <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
                <TotalClientsCard isLoading={isLoading} total={dashboardData.totalClients} error={error} />
              </Grid>
              <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
                <TotalRevenueCard isLoading={isLoading} total={dashboardData.totalRevenue} error={error} />
              </Grid>
            </Grid>
          </Grid>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: { xs: 'center', md: 'flex-end' },
                mb: 2,
                gap: 1,
                flexDirection: { xs: 'column', sm: 'row' }
              }}
            >
              <StandardButton
                variant={useMetricsCard ? 'primary' : 'outline'}
                startIcon={<AssessmentIcon />}
                onClick={() => setUseMetricsCard(true)}
                size="small"
                sx={{ width: { xs: '100%', sm: 'auto' } }}
              >
                Métriques
              </StandardButton>
              <StandardButton
                variant={!useMetricsCard ? 'primary' : 'outline'}
                startIcon={<BarChartIcon />}
                onClick={() => setUseMetricsCard(false)}
                size="small"
                sx={{ width: { xs: '100%', sm: 'auto' } }}
              >
                Graphique
              </StandardButton>
            </Box>
            <Grid container spacing={gridSpacing}>
              <Grid size={{ xs: 12, md: 8 }}>
                {useMetricsCard ? (
                  <SalesMetricsCard isLoading={isLoading} data={dashboardData.salesData} error={error} />
                ) : (
                  <SimpleSalesChart isLoading={isLoading} data={dashboardData.salesData} error={error} />
                )}
              </Grid>
              <Grid size={{ xs: 12, md: 4 }}>
                <RecentOrdersCard isLoading={isLoading} orders={dashboardData.recentOrders} error={error} />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </MainCard>
  );
}
