import axios from 'axios';

const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Create axios instance with auth headers
const axiosInstance = axios.create({
  baseURL: API_URL,
  timeout: 10000,
});

// Add auth headers to requests
axiosInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Helper function to get status label
const getStatusLabel = (status) => {
  const statusMap = {
    en_attente: 'En attente',
    confirmee: 'Confirmée',
    en_preparation: 'En préparation',
    expediee: 'Expédiée',
    livree: 'Livrée',
    annulee: 'Annulée',
    remboursee: '<PERSON><PERSON><PERSON><PERSON>',
    retournee: 'Retournée'
  };

  return statusMap[status] || status || 'En attente';
};

// Helper function to fetch all orders across all pages
async function fetchAllOrders() {
  try {
    console.log('🔄 Fetching all orders for accurate statistics...');

    // First, get the first page to know total count and pagination info
    const firstPageResponse = await axiosInstance.get('/commandes', {
      params: { with: 'user,client', per_page: 100 } // Get more per page to reduce API calls
    });

    if (!firstPageResponse.data?.success || !firstPageResponse.data?.data) {
      console.warn('⚠️ Invalid first page response format');
      return { orders: [], totalCount: 0 };
    }

    const paginationData = firstPageResponse.data.data;
    const totalCount = paginationData.total || 0;
    const lastPage = paginationData.last_page || 1;
    const perPage = paginationData.per_page || 15;

    console.log(`📊 Orders pagination info: ${totalCount} total orders, ${lastPage} pages, ${perPage} per page`);

    let allOrders = [...(paginationData.data || [])];

    // If there are more pages, fetch them
    if (lastPage > 1) {
      console.log(`🔄 Fetching remaining ${lastPage - 1} pages...`);

      const remainingPagePromises = [];
      for (let page = 2; page <= lastPage; page++) {
        remainingPagePromises.push(
          axiosInstance.get('/commandes', {
            params: { with: 'user,client', per_page: perPage, page }
          })
        );
      }

      const remainingPagesResults = await Promise.allSettled(remainingPagePromises);

      remainingPagesResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value?.data?.success) {
          const pageOrders = result.value.data.data?.data || [];
          allOrders = [...allOrders, ...pageOrders];
          console.log(`✅ Page ${index + 2} loaded: ${pageOrders.length} orders`);
        } else {
          console.error(`❌ Failed to load page ${index + 2}:`, result.reason?.message);
        }
      });
    }

    console.log(`✅ Total orders loaded: ${allOrders.length} out of ${totalCount}`);

    return {
      orders: allOrders,
      totalCount: totalCount,
      actualLoadedCount: allOrders.length
    };
  } catch (error) {
    console.error('❌ Error fetching all orders:', error);
    return { orders: [], totalCount: 0, actualLoadedCount: 0 };
  }
}

// Dashboard metrics service
export async function fetchDashboardMetrics() {
  try {
    console.log('🔄 Fetching dashboard data from API:', API_URL);
    console.log('🔑 Using token:', localStorage.getItem('access_token') ? 'Token found' : 'No token');

    // Fetch all required data in parallel with proper error handling
    const [productsRes, allOrdersRes, clientsRes] = await Promise.allSettled([
      axiosInstance.get('/produits'),
      fetchAllOrders(), // Use our new function to get all orders
      axiosInstance.get('/clients')
    ]);

    console.log('📊 API responses:', {
      products: {
        status: productsRes.status,
        fulfilled: productsRes.status === 'fulfilled',
        error: productsRes.status === 'rejected' ? productsRes.reason?.message : null
      },
      orders: {
        status: allOrdersRes.status,
        fulfilled: allOrdersRes.status === 'fulfilled',
        error: allOrdersRes.status === 'rejected' ? allOrdersRes.reason?.message : null
      },
      clients: {
        status: clientsRes.status,
        fulfilled: clientsRes.status === 'fulfilled',
        error: clientsRes.status === 'rejected' ? clientsRes.reason?.message : null
      }
    });

    // Handle individual responses gracefully
    let productsData = { data: [] };
    let ordersData = { data: [], totalCount: 0 };
    let clientsData = { data: [] };

    if (productsRes.status === 'fulfilled') {
      const response = productsRes.value.data;
      console.log('✅ Products full response:', response);

      // Products API returns: {"success": true, "data": [...]}
      if (response && response.success && response.data) {
        productsData = { data: response.data };
        console.log('✅ Products data loaded:', response.data.length, 'products');
      } else {
        console.warn('⚠️ Products response format unexpected:', response);
      }
    } else {
      console.error('❌ Products fetch failed:', productsRes.reason);
    }

    if (allOrdersRes.status === 'fulfilled') {
      const response = allOrdersRes.value;
      console.log('✅ All orders response:', {
        totalCount: response.totalCount,
        actualLoaded: response.actualLoadedCount
      });

      ordersData = {
        data: response.orders,
        totalCount: response.totalCount,
        actualLoadedCount: response.actualLoadedCount
      };
      console.log('✅ Orders data loaded:', response.actualLoadedCount, 'orders out of', response.totalCount, 'total');
    } else {
      console.error('❌ Orders fetch failed:', allOrdersRes.reason);
    }

    if (clientsRes.status === 'fulfilled') {
      const response = clientsRes.value.data;
      console.log('✅ Clients full response:', response);

      // Clients API returns: [{...}, {...}] (direct array)
      if (Array.isArray(response)) {
        clientsData = { data: response };
        console.log('✅ Clients data loaded:', response.length, 'clients');
      } else if (response && response.success && response.data) {
        // Fallback for wrapped format
        clientsData = { data: response.data };
        console.log('✅ Clients data loaded (wrapped):', response.data.length, 'clients');
      } else {
        console.warn('⚠️ Clients response format unexpected:', response);
      }
    } else {
      console.error('❌ Clients fetch failed:', clientsRes.reason);
    }

    // Extract totals - use the correct total count from API
    const totalProducts = Array.isArray(productsData.data) ? productsData.data.length :
                         Array.isArray(productsData) ? productsData.length : 0;

    // Use the total count from pagination metadata, not just loaded orders
    const totalOrders = ordersData.totalCount || ordersData.actualLoadedCount || 0;

    const totalClients = Array.isArray(clientsData.data) ? clientsData.data.length :
                        Array.isArray(clientsData) ? clientsData.length : 0;

    // Get recent orders (last 5) with improved data extraction
    const ordersArray = ordersData.data || [];
    const recentOrders = Array.isArray(ordersArray) ?
      ordersArray
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5)
        .map(order => ({
          id: order.id,
          order_number: order.numero_commande || `CMD-${String(order.id).padStart(6, '0')}`,
          customer_name: order.user?.name || order.client?.nom || order.client?.name || order.nom_client || 'Client inconnu',
          total: parseFloat(order.total_commande || order.total || 0),
          status: getStatusLabel(order.status || order.statut_commande || 'en_attente'),
          created_at: order.created_at
        })) : [];

    console.log('📋 Recent orders processed:', recentOrders.length);

    // Calculate total revenue from ALL loaded orders
    const totalRevenue = Array.isArray(ordersArray) ?
      ordersArray.reduce((sum, order) => {
        return sum + parseFloat(order.total_commande || order.total || 0);
      }, 0) : 0;

    console.log('💰 Total revenue calculated from', ordersArray.length, 'orders:', totalRevenue, 'DT');

    // Calculate sales data for the chart (last 6 months)
    const salesData = calculateSalesData(ordersArray);

    return {
      totalProducts,
      totalOrders,
      totalClients,
      totalRevenue,
      recentOrders,
      salesData
    };
  } catch (error) {
    console.error('Error fetching dashboard metrics:', error);
    throw error;
  }
}

// Helper function to calculate sales data for charts
function calculateSalesData(orders) {
  if (!Array.isArray(orders)) {
    console.log('⚠️ Orders is not an array:', typeof orders);
    return [];
  }

  const now = new Date();

  // Initialize months array for last 6 months
  const months = [];
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    months.push({
      month: date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' }),
      sales: 0,
      orderCount: 0,
      monthKey: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    });
  }

  console.log('📅 Initialized months:', months.map(m => m.month));

  // Aggregate sales by month
  orders.forEach(order => {
    if (!order.created_at) return;

    const orderDate = new Date(order.created_at);
    const orderMonthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;

    const monthIndex = months.findIndex(m => m.monthKey === orderMonthKey);

    if (monthIndex !== -1) {
      const orderTotal = parseFloat(order.total_commande || order.total || 0);
      months[monthIndex].sales += orderTotal;
      months[monthIndex].orderCount += 1;

      console.log(`📊 Added order ${order.id}: ${orderTotal} DT to ${months[monthIndex].month}`);
    }
  });

  console.log('📈 Final sales data:', months);
  return months;
}

// Fetch specific metrics
export async function fetchProductsCount() {
  try {
    const response = await axiosInstance.get('/produits');
    const data = response.data;
    // Products API returns: {"success": true, "data": [...]}
    if (data && data.success && Array.isArray(data.data)) {
      return data.data.length;
    }
    return 0;
  } catch (error) {
    console.error('Error fetching products count:', error);
    return 0;
  }
}

export async function fetchOrdersCount() {
  try {
    const response = await axiosInstance.get('/commandes');
    const data = response.data;
    // Orders API returns: {"success": true, "data": {"current_page": 1, "data": [...], "total": 43}}
    // Use the total count from pagination metadata, not the current page count
    if (data && data.success && data.data && typeof data.data.total === 'number') {
      console.log('✅ Orders total count from API:', data.data.total);
      return data.data.total;
    }
    // Fallback to counting current page items if total is not available
    if (data && data.success && data.data && Array.isArray(data.data.data)) {
      console.warn('⚠️ Using fallback count from current page:', data.data.data.length);
      return data.data.data.length;
    }
    return 0;
  } catch (error) {
    console.error('Error fetching orders count:', error);
    return 0;
  }
}

export async function fetchClientsCount() {
  try {
    const response = await axiosInstance.get('/clients');
    const data = response.data;
    // Clients API returns: [{...}, {...}] (direct array)
    if (Array.isArray(data)) {
      return data.length;
    }
    return 0;
  } catch (error) {
    console.error('Error fetching clients count:', error);
    return 0;
  }
}

export async function fetchRecentOrders(limit = 5) {
  try {
    const response = await axiosInstance.get('/commandes', {
      params: { with: 'user,client' }
    });
    const data = response.data;

    // Orders API returns: {"success": true, "data": {"current_page": 1, "data": [...]}}
    let ordersArray = [];
    if (data && data.success && data.data && Array.isArray(data.data.data)) {
      ordersArray = data.data.data;
    }

    return Array.isArray(ordersArray) ?
      ordersArray
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, limit)
        .map(order => ({
          id: order.id,
          order_number: order.numero_commande || `CMD-${String(order.id).padStart(6, '0')}`,
          customer_name: order.user?.name || order.client?.nom || order.client?.name || order.nom_client || 'Client inconnu',
          total: parseFloat(order.total_commande || order.total || 0),
          status: getStatusLabel(order.status || order.statut_commande || 'en_attente'),
          created_at: order.created_at
        })) : [];
  } catch (error) {
    console.error('Error fetching recent orders:', error);
    return [];
  }
}
