import React from 'react';
import PropTypes from 'prop-types';

// material-ui
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Card,
  CardContent,
  Divider,
  Grid,
  Typography,
  LinearProgress,
  Chip
} from '@mui/material';

// material-ui icons
import {
  AccessTime as TimeIcon,
  Psychology as PredictionIcon,
  SentimentSatisfied as SatisfiedIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as UnsatisfiedIcon,
  TrendingUp as ConfidenceIcon,
  Star as CommonIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import { gridSpacing } from 'store/constant';

export default function AnalyticsMetricsCard({ isLoading = false, data = null, error = null }) {
  const theme = useTheme();

  // Static data for demonstration
  const staticData = {
    session_duration: 245.5, // in seconds
    total_predictions: 1247,
    satisfied_count: 856,
    neutral_count: 234,
    unsatisfied_count: 157,
    average_confidence: 0.847, // 84.7%
    most_common_prediction: 'Produit Recommandé'
  };

  // Use provided data or fall back to static data
  const metrics = data || staticData;

  // Calculate percentages for sentiment distribution
  const totalSentiments = metrics.satisfied_count + metrics.neutral_count + metrics.unsatisfied_count;
  const satisfiedPercentage = totalSentiments > 0 ? (metrics.satisfied_count / totalSentiments) * 100 : 0;
  const neutralPercentage = totalSentiments > 0 ? (metrics.neutral_count / totalSentiments) * 100 : 0;
  const unsatisfiedPercentage = totalSentiments > 0 ? (metrics.unsatisfied_count / totalSentiments) * 100 : 0;

  // Format session duration
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Format confidence percentage
  const formatConfidence = (confidence) => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: 'error.lighter',
                borderRadius: 2,
                border: 2,
                borderColor: 'error.main'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données d'analyse: {error}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard>
      <Grid container spacing={gridSpacing}>
        <Grid size={12}>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Métriques d'Analyse Prédictive
          </Typography>
        </Grid>

        {/* Top row - Key metrics */}
        <Grid size={12}>
          <Grid container spacing={2}>
            {/* Session Duration */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ bgcolor: 'primary.lighter', border: 1, borderColor: 'primary.light' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <TimeIcon sx={{ color: 'primary.main', mr: 1 }} />
                    <Typography variant="body2" color="primary.main">
                      Durée Session
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'primary.dark' }}>
                    {formatDuration(metrics.session_duration)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Total Predictions */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ bgcolor: 'secondary.lighter', border: 1, borderColor: 'secondary.light' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <PredictionIcon sx={{ color: 'secondary.main', mr: 1 }} />
                    <Typography variant="body2" color="secondary.main">
                      Prédictions Totales
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'secondary.dark' }}>
                    {metrics.total_predictions.toLocaleString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Average Confidence */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ bgcolor: 'success.lighter', border: 1, borderColor: 'success.light' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <ConfidenceIcon sx={{ color: 'success.main', mr: 1 }} />
                    <Typography variant="body2" color="success.main">
                      Confiance Moyenne
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'success.dark' }}>
                    {formatConfidence(metrics.average_confidence)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Most Common Prediction */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ bgcolor: 'warning.lighter', border: 1, borderColor: 'warning.light' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <CommonIcon sx={{ color: 'warning.main', mr: 1 }} />
                    <Typography variant="body2" color="warning.main">
                      Prédiction Commune
                    </Typography>
                  </Box>
                  <Typography variant="h6" sx={{ color: 'warning.dark', fontSize: '1rem' }}>
                    {metrics.most_common_prediction}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        <Grid size={12}>
          <Divider sx={{ my: 2 }} />
        </Grid>

        {/* Sentiment Analysis Section */}
        <Grid size={12}>
          <Typography variant="subtitle2" sx={{ mb: 2 }}>
            Analyse des Sentiments
          </Typography>
        </Grid>

        <Grid size={12}>
          <Grid container spacing={2}>
            {/* Satisfied */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Box sx={{ p: 2, bgcolor: 'success.lighter', borderRadius: 1, border: 1, borderColor: 'success.light' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <SatisfiedIcon sx={{ color: 'success.main', mr: 1 }} />
                  <Typography variant="body2" color="success.main" fontWeight="medium">
                    Satisfaits
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ color: 'success.dark', mb: 1 }}>
                  {metrics.satisfied_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={satisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'success.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'success.main'
                    }
                  }}
                />
                <Typography variant="body2" sx={{ color: 'success.dark', mt: 0.5 }}>
                  {satisfiedPercentage.toFixed(1)}%
                </Typography>
              </Box>
            </Grid>

            {/* Neutral */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Box sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 1, border: 1, borderColor: 'grey.300' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <NeutralIcon sx={{ color: 'grey.600', mr: 1 }} />
                  <Typography variant="body2" color="grey.700" fontWeight="medium">
                    Neutres
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ color: 'grey.800', mb: 1 }}>
                  {metrics.neutral_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={neutralPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'grey.600'
                    }
                  }}
                />
                <Typography variant="body2" sx={{ color: 'grey.700', mt: 0.5 }}>
                  {neutralPercentage.toFixed(1)}%
                </Typography>
              </Box>
            </Grid>

            {/* Unsatisfied */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Box sx={{ p: 2, bgcolor: 'error.lighter', borderRadius: 1, border: 1, borderColor: 'error.light' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <UnsatisfiedIcon sx={{ color: 'error.main', mr: 1 }} />
                  <Typography variant="body2" color="error.main" fontWeight="medium">
                    Insatisfaits
                  </Typography>
                </Box>
                <Typography variant="h4" sx={{ color: 'error.dark', mb: 1 }}>
                  {metrics.unsatisfied_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={unsatisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'error.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'error.main'
                    }
                  }}
                />
                <Typography variant="body2" sx={{ color: 'error.dark', mt: 0.5 }}>
                  {unsatisfiedPercentage.toFixed(1)}%
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Grid>

        <Grid size={12}>
          <Divider sx={{ my: 2 }} />
        </Grid>

        {/* Summary Statistics */}
        <Grid size={12}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Taux de Satisfaction
                </Typography>
                <Typography variant="h5" color="success.main">
                  {satisfiedPercentage.toFixed(1)}%
                </Typography>
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Niveau de Confiance
                </Typography>
                <Chip
                  label={metrics.average_confidence >= 0.8 ? 'Élevé' : metrics.average_confidence >= 0.6 ? 'Moyen' : 'Faible'}
                  color={metrics.average_confidence >= 0.8 ? 'success' : metrics.average_confidence >= 0.6 ? 'warning' : 'error'}
                  variant="filled"
                />
              </Box>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}

AnalyticsMetricsCard.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.object,
  error: PropTypes.string
};
