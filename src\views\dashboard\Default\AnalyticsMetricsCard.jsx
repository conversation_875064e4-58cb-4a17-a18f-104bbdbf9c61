import React from 'react';
import PropTypes from 'prop-types';

// material-ui
import { useTheme } from '@mui/material/styles';
import { Box, Card, CardContent, Divider, Grid, Typography, LinearProgress, Chip, Avatar, Stack, Paper } from '@mui/material';

// material-ui icons
import {
  AccessTime as TimeIcon,
  Psychology as PredictionIcon,
  SentimentSatisfied as SatisfiedIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as UnsatisfiedIcon,
  TrendingUp as ConfidenceIcon,
  Star as CommonIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import { gridSpacing } from 'store/constant';

export default function AnalyticsMetricsCard({ isLoading = false, data = null, error = null }) {
  const theme = useTheme();

  // Static data for demonstration
  const staticData = {
    session_duration: 245.5, // in seconds
    total_predictions: 1247,
    satisfied_count: 856,
    neutral_count: 234,
    unsatisfied_count: 157,
    average_confidence: 0.847, // 84.7%
    most_common_prediction: 'Produit Recommandé'
  };

  // Use provided data or fall back to static data
  const metrics = data || staticData;

  // Calculate percentages for sentiment distribution
  const totalSentiments = metrics.satisfied_count + metrics.neutral_count + metrics.unsatisfied_count;
  const satisfiedPercentage = totalSentiments > 0 ? (metrics.satisfied_count / totalSentiments) * 100 : 0;
  const neutralPercentage = totalSentiments > 0 ? (metrics.neutral_count / totalSentiments) * 100 : 0;
  const unsatisfiedPercentage = totalSentiments > 0 ? (metrics.unsatisfied_count / totalSentiments) * 100 : 0;

  // Format session duration
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Format confidence percentage
  const formatConfidence = (confidence) => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: 'error.lighter',
                borderRadius: 2,
                border: 2,
                borderColor: 'error.main'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données d'analyse: {error}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard>
      <Grid container spacing={gridSpacing}>
        {/* Header */}
        <Grid size={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <AnalyticsIcon sx={{ color: 'primary.main', mr: 2, fontSize: '2rem' }} />
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                Métriques d'Analyse Prédictive
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Analyse des Sentiments
              </Typography>
            </Box>
          </Box>
        </Grid>

        {/* Top row - Key metrics */}
        <Grid size={12}>
          <Grid container spacing={2}>
            {/* Session Duration */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'divider' }}>
                <TimeIcon sx={{ color: 'primary.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Durée Session
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {formatDuration(metrics.session_duration)}
                </Typography>
              </Card>
            </Grid>

            {/* Total Predictions */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'divider' }}>
                <PredictionIcon sx={{ color: 'secondary.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Prédictions Totales
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {metrics.total_predictions.toLocaleString()}
                </Typography>
              </Card>
            </Grid>

            {/* Average Confidence */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'divider' }}>
                <ConfidenceIcon sx={{ color: 'success.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Confiance Moyenne
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {formatConfidence(metrics.average_confidence)}
                </Typography>
              </Card>
            </Grid>

            {/* Most Common Prediction */}
            <Grid size={{ xs: 12, sm: 6, md: 3 }}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'divider' }}>
                <CommonIcon sx={{ color: 'warning.main', fontSize: '2rem', mb: 1 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Prédiction Commune
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {metrics.most_common_prediction}
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Sentiment Analysis Section */}
        <Grid size={12}>
          <Divider sx={{ my: 2 }} />
          <Grid container spacing={2}>
            {/* Satisfied */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Card sx={{ p: 2, border: '1px solid', borderColor: 'success.light', bgcolor: 'success.lighter' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SatisfiedIcon sx={{ color: 'success.main', mr: 1 }} />
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      Satisfaits
                    </Typography>
                  </Box>
                  <Chip
                    label={`${satisfiedPercentage.toFixed(1)}%`}
                    size="small"
                    sx={{ bgcolor: 'success.main', color: 'white', fontWeight: 600 }}
                  />
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                  {metrics.satisfied_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={satisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'success.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'success.main',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>

            {/* Neutral */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Card sx={{ p: 2, border: '1px solid', borderColor: 'grey.300', bgcolor: 'grey.50' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <NeutralIcon sx={{ color: 'grey.600', mr: 1 }} />
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      Neutres
                    </Typography>
                  </Box>
                  <Chip
                    label={`${neutralPercentage.toFixed(1)}%`}
                    size="small"
                    sx={{ bgcolor: 'grey.600', color: 'white', fontWeight: 600 }}
                  />
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                  {metrics.neutral_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={neutralPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'grey.600',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>

            {/* Unsatisfied */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Card sx={{ p: 2, border: '1px solid', borderColor: 'error.light', bgcolor: 'error.lighter' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <UnsatisfiedIcon sx={{ color: 'error.main', mr: 1 }} />
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      Insatisfaits
                    </Typography>
                  </Box>
                  <Chip
                    label={`${unsatisfiedPercentage.toFixed(1)}%`}
                    size="small"
                    sx={{ bgcolor: 'error.main', color: 'white', fontWeight: 600 }}
                  />
                </Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                  {metrics.unsatisfied_count}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={unsatisfiedPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'error.lighter',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: 'error.main',
                      borderRadius: 4
                    }
                  }}
                />
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Summary Statistics */}
        <Grid size={12}>
          <Divider sx={{ my: 2 }} />
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'divider' }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Taux de Satisfaction
                </Typography>
                <Typography variant="h3" sx={{ fontWeight: 700, color: 'success.main' }}>
                  {satisfiedPercentage.toFixed(1)}%
                </Typography>
              </Card>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Card sx={{ p: 2, textAlign: 'center', border: '1px solid', borderColor: 'divider' }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Niveau de Confiance
                </Typography>
                <Chip
                  label={metrics.average_confidence >= 0.8 ? 'Élevé' : metrics.average_confidence >= 0.6 ? 'Moyen' : 'Faible'}
                  color={metrics.average_confidence >= 0.8 ? 'success' : metrics.average_confidence >= 0.6 ? 'warning' : 'error'}
                  sx={{ fontWeight: 600, fontSize: '1rem', height: 36 }}
                />
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </MainCard>
  );
}

AnalyticsMetricsCard.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.object,
  error: PropTypes.string
};
