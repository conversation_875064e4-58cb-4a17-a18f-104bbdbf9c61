import React from 'react';
import PropTypes from 'prop-types';

// material-ui
import { useTheme } from '@mui/material/styles';
import { Box, Card, CardContent, Divider, Grid, Typography, LinearProgress, Chip, Avatar, Stack, Paper } from '@mui/material';

// material-ui icons
import {
  AccessTime as TimeIcon,
  Psychology as PredictionIcon,
  SentimentSatisfied as SatisfiedIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as UnsatisfiedIcon,
  TrendingUp as ConfidenceIcon,
  Star as CommonIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import { gridSpacing } from 'store/constant';

export default function AnalyticsMetricsCard({ isLoading = false, data = null, error = null }) {
  const theme = useTheme();

  // Static data for demonstration
  const staticData = {
    session_duration: 245.5, // in seconds
    total_predictions: 1247,
    satisfied_count: 856,
    neutral_count: 234,
    unsatisfied_count: 157,
    average_confidence: 0.847, // 84.7%
    most_common_prediction: 'Produit Recommandé'
  };

  // Use provided data or fall back to static data
  const metrics = data || staticData;

  // Calculate percentages for sentiment distribution
  const totalSentiments = metrics.satisfied_count + metrics.neutral_count + metrics.unsatisfied_count;
  const satisfiedPercentage = totalSentiments > 0 ? (metrics.satisfied_count / totalSentiments) * 100 : 0;
  const neutralPercentage = totalSentiments > 0 ? (metrics.neutral_count / totalSentiments) * 100 : 0;
  const unsatisfiedPercentage = totalSentiments > 0 ? (metrics.unsatisfied_count / totalSentiments) * 100 : 0;

  // Format session duration
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Format confidence percentage
  const formatConfidence = (confidence) => {
    return `${(confidence * 100).toFixed(1)}%`;
  };

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: 'error.lighter',
                borderRadius: 2,
                border: 2,
                borderColor: 'error.main'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données d'analyse: {error}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  if (isLoading) {
    return <SkeletonTotalGrowthBarChart />;
  }

  return (
    <MainCard
      sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white', position: 'relative', overflow: 'hidden' }}
    >
      {/* Background decoration */}
      <Box
        sx={{
          position: 'absolute',
          top: -50,
          right: -50,
          width: 200,
          height: 200,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.1)',
          zIndex: 0
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: -30,
          left: -30,
          width: 150,
          height: 150,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.05)',
          zIndex: 0
        }}
      />

      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <Grid container spacing={3}>
          {/* Header */}
          <Grid size={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  mr: 2,
                  width: 48,
                  height: 48
                }}
              >
                <AnalyticsIcon fontSize="large" />
              </Avatar>
              <Box>
                <Typography variant="h5" sx={{ color: 'white', fontWeight: 600, mb: 0.5 }}>
                  Métriques d'Analyse Prédictive
                </Typography>
                <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  Analyse des Sentiments
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Top row - Key metrics */}
          <Grid size={12}>
            <Grid container spacing={2}>
              {/* Session Duration */}
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2.5,
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      background: 'rgba(255, 255, 255, 0.2)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                    <Avatar sx={{ bgcolor: 'rgba(33, 150, 243, 0.2)', color: '#2196F3', mr: 1.5, width: 32, height: 32 }}>
                      <TimeIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 500 }}>
                      Durée Session
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'white', fontWeight: 700, fontSize: '1.8rem' }}>
                    {formatDuration(metrics.session_duration)}
                  </Typography>
                </Paper>
              </Grid>

              {/* Total Predictions */}
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2.5,
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      background: 'rgba(255, 255, 255, 0.2)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                    <Avatar sx={{ bgcolor: 'rgba(156, 39, 176, 0.2)', color: '#9C27B0', mr: 1.5, width: 32, height: 32 }}>
                      <PredictionIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 500 }}>
                      Prédictions Totales
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'white', fontWeight: 700, fontSize: '1.8rem' }}>
                    {metrics.total_predictions.toLocaleString()}
                  </Typography>
                </Paper>
              </Grid>

              {/* Average Confidence */}
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2.5,
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      background: 'rgba(255, 255, 255, 0.2)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                    <Avatar sx={{ bgcolor: 'rgba(76, 175, 80, 0.2)', color: '#4CAF50', mr: 1.5, width: 32, height: 32 }}>
                      <ConfidenceIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 500 }}>
                      Confiance Moyenne
                    </Typography>
                  </Box>
                  <Typography variant="h4" sx={{ color: 'white', fontWeight: 700, fontSize: '1.8rem' }}>
                    {formatConfidence(metrics.average_confidence)}
                  </Typography>
                </Paper>
              </Grid>

              {/* Most Common Prediction */}
              <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2.5,
                    background: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      background: 'rgba(255, 255, 255, 0.2)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                    <Avatar sx={{ bgcolor: 'rgba(255, 193, 7, 0.2)', color: '#FFC107', mr: 1.5, width: 32, height: 32 }}>
                      <CommonIcon fontSize="small" />
                    </Avatar>
                    <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 500 }}>
                      Prédiction Commune
                    </Typography>
                  </Box>
                  <Typography variant="h6" sx={{ color: 'white', fontWeight: 600, fontSize: '1.1rem' }}>
                    {metrics.most_common_prediction}
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Grid>

          {/* Sentiment Analysis Section */}
          <Grid size={12}>
            <Grid container spacing={3}>
              {/* Satisfied */}
              <Grid size={{ xs: 12, md: 4 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    background: 'rgba(76, 175, 80, 0.15)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(76, 175, 80, 0.3)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      background: 'rgba(76, 175, 80, 0.2)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: 'rgba(76, 175, 80, 0.3)', color: '#4CAF50', mr: 1.5, width: 36, height: 36 }}>
                        <SatisfiedIcon />
                      </Avatar>
                      <Typography variant="body1" sx={{ color: 'white', fontWeight: 600 }}>
                        Satisfaits
                      </Typography>
                    </Box>
                    <Chip
                      label={`${satisfiedPercentage.toFixed(1)}%`}
                      sx={{
                        bgcolor: 'rgba(76, 175, 80, 0.3)',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '0.75rem'
                      }}
                    />
                  </Box>
                  <Typography variant="h3" sx={{ color: 'white', fontWeight: 700, mb: 2 }}>
                    {metrics.satisfied_count}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={satisfiedPercentage}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: 'rgba(76, 175, 80, 0.2)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: '#4CAF50',
                        borderRadius: 3
                      }
                    }}
                  />
                </Paper>
              </Grid>

              {/* Neutral */}
              <Grid size={{ xs: 12, md: 4 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    background: 'rgba(158, 158, 158, 0.15)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(158, 158, 158, 0.3)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      background: 'rgba(158, 158, 158, 0.2)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: 'rgba(158, 158, 158, 0.3)', color: '#9E9E9E', mr: 1.5, width: 36, height: 36 }}>
                        <NeutralIcon />
                      </Avatar>
                      <Typography variant="body1" sx={{ color: 'white', fontWeight: 600 }}>
                        Neutres
                      </Typography>
                    </Box>
                    <Chip
                      label={`${neutralPercentage.toFixed(1)}%`}
                      sx={{
                        bgcolor: 'rgba(158, 158, 158, 0.3)',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '0.75rem'
                      }}
                    />
                  </Box>
                  <Typography variant="h3" sx={{ color: 'white', fontWeight: 700, mb: 2 }}>
                    {metrics.neutral_count}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={neutralPercentage}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: 'rgba(158, 158, 158, 0.2)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: '#9E9E9E',
                        borderRadius: 3
                      }
                    }}
                  />
                </Paper>
              </Grid>

              {/* Unsatisfied */}
              <Grid size={{ xs: 12, md: 4 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    background: 'rgba(244, 67, 54, 0.15)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(244, 67, 54, 0.3)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      background: 'rgba(244, 67, 54, 0.2)'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ bgcolor: 'rgba(244, 67, 54, 0.3)', color: '#F44336', mr: 1.5, width: 36, height: 36 }}>
                        <UnsatisfiedIcon />
                      </Avatar>
                      <Typography variant="body1" sx={{ color: 'white', fontWeight: 600 }}>
                        Insatisfaits
                      </Typography>
                    </Box>
                    <Chip
                      label={`${unsatisfiedPercentage.toFixed(1)}%`}
                      sx={{
                        bgcolor: 'rgba(244, 67, 54, 0.3)',
                        color: 'white',
                        fontWeight: 600,
                        fontSize: '0.75rem'
                      }}
                    />
                  </Box>
                  <Typography variant="h3" sx={{ color: 'white', fontWeight: 700, mb: 2 }}>
                    {metrics.unsatisfied_count}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={unsatisfiedPercentage}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: 'rgba(244, 67, 54, 0.2)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: '#F44336',
                        borderRadius: 3
                      }
                    }}
                  />
                </Paper>
              </Grid>
            </Grid>
          </Grid>

          {/* Summary Statistics */}
          <Grid size={12}>
            <Grid container spacing={3}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)', mb: 1 }}>
                    Taux de Satisfaction
                  </Typography>
                  <Typography variant="h3" sx={{ color: 'white', fontWeight: 700 }}>
                    {satisfiedPercentage.toFixed(1)}%
                  </Typography>
                </Paper>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: 3,
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)', mb: 1 }}>
                    Niveau de Confiance
                  </Typography>
                  <Chip
                    label={metrics.average_confidence >= 0.8 ? 'Élevé' : metrics.average_confidence >= 0.6 ? 'Moyen' : 'Faible'}
                    sx={{
                      bgcolor:
                        metrics.average_confidence >= 0.8
                          ? 'rgba(76, 175, 80, 0.3)'
                          : metrics.average_confidence >= 0.6
                            ? 'rgba(255, 193, 7, 0.3)'
                            : 'rgba(244, 67, 54, 0.3)',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '1rem',
                      height: 36,
                      border: `1px solid ${
                        metrics.average_confidence >= 0.8
                          ? 'rgba(76, 175, 80, 0.5)'
                          : metrics.average_confidence >= 0.6
                            ? 'rgba(255, 193, 7, 0.5)'
                            : 'rgba(244, 67, 54, 0.5)'
                      }`
                    }}
                  />
                </Paper>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </MainCard>
  );
}

AnalyticsMetricsCard.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.object,
  error: PropTypes.string
};
